<div class="group-based-mapping" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and configuration...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    
    <!-- Step Navigation -->
    <div class="step-navigation">
      <div class="step-tabs">
        <button 
          class="step-tab" 
          [class.active]="currentStep === 1"
          [class.completed]="departmentGroups.length > 0"
          (click)="goToStep(1)">
          <mat-icon>group_add</mat-icon>
          <span>1. Create Groups</span>
        </button>
        
        <button 
          class="step-tab" 
          [class.active]="currentStep === 2"
          [class.completed]="groupCategoryMappings.length > 0"
          [disabled]="!canProceedToStep(2)"
          (click)="goToStep(2)">
          <mat-icon>category</mat-icon>
          <span>2. Map Categories</span>
        </button>
        
        <button 
          class="step-tab" 
          [class.active]="currentStep === 3"
          [class.completed]="groupWorkareaMappings.length > 0"
          [disabled]="!canProceedToStep(3)"
          (click)="goToStep(3)">
          <mat-icon>work</mat-icon>
          <span>3. Map Workareas</span>
        </button>
      </div>
    </div>

    <!-- Step 1: Create Department Groups -->
    <div *ngIf="currentStep === 1" class="step-content">
      <div class="step-header">
        <mat-icon>group_add</mat-icon>
        <h3>Create Department Groups</h3>
        <p>Group related departments together for easier management</p>
      </div>

      <!-- Group Creation Form -->
      <mat-card class="creation-card">
        <mat-card-header>
          <mat-card-title>Create New Group</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <mat-form-field appearance="outline" class="group-name-field">
              <mat-label>Group Name</mat-label>
              <input matInput [formControl]="newGroupNameCtrl" placeholder="e.g., Food Department, Beverage Department">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="department-select-field">
              <mat-label>Select Departments</mat-label>
              <mat-select [formControl]="selectedDepartmentsForGroupCtrl" multiple>
                <mat-option>
                  <ngx-mat-select-search 
                    [formControl]="departmentFilterCtrl"
                    placeholderLabel="Search departments..."
                    noEntriesFoundLabel="No departments found">
                  </ngx-mat-select-search>
                </mat-option>
                
                <mat-option *ngFor="let department of filteredDepartments" [value]="department.id">
                  {{department.name}} <span *ngIf="department.code">({{department.code}})</span>
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="form-actions">
            <button mat-raised-button color="primary" (click)="createDepartmentGroup()"
                    [disabled]="!newGroupNameCtrl.value || !selectedDepartmentsForGroupCtrl.value?.length">
              <mat-icon>add</mat-icon>
              Create Group
            </button>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Existing Groups -->
      <div class="existing-groups" *ngIf="departmentGroups.length > 0">
        <h4>Created Groups ({{departmentGroups.length}})</h4>
        <div class="groups-grid">
          <mat-card *ngFor="let group of departmentGroups" class="group-card">
            <mat-card-header>
              <mat-card-title>{{group.groupName}}</mat-card-title>
              <button mat-icon-button (click)="deleteDepartmentGroup(group.groupId)" class="delete-btn">
                <mat-icon>delete</mat-icon>
              </button>
            </mat-card-header>
            <mat-card-content>
              <div class="department-chips">
                <mat-chip-listbox>
                  <mat-chip *ngFor="let deptId of group.departmentIds">
                    {{getDepartmentName(deptId)}}
                  </mat-chip>
                </mat-chip-listbox>
              </div>
              <div class="group-stats">
                <span class="stat">{{group.departmentIds.length}} departments</span>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="departmentGroups.length === 0" class="empty-state">
        <mat-icon class="empty-icon">group_add</mat-icon>
        <h4>No Groups Created Yet</h4>
        <p>Create your first department group to get started with the mapping process.</p>
      </div>

      <!-- Step Actions -->
      <div class="step-actions" *ngIf="departmentGroups.length > 0">
        <button mat-raised-button color="primary" (click)="goToStep(2)">
          Next: Map Categories
          <mat-icon>arrow_forward</mat-icon>
        </button>
      </div>
    </div>

    <!-- Step 2: Group-Category Mapping -->
    <div *ngIf="currentStep === 2" class="step-content">
      <div class="step-header">
        <mat-icon>category</mat-icon>
        <h3>Map Groups to Categories</h3>
        <p>Assign categories to each department group</p>
      </div>

      <app-group-category-mapping
        [departmentGroups]="departmentGroups"
        [availableCategories]="availableCategories"
        [existingMappings]="groupCategoryMappings"
        (mappingChanged)="onGroupCategoryMappingChanged($event)">
      </app-group-category-mapping>

      <!-- Step Actions -->
      <div class="step-actions">
        <button mat-button (click)="goToStep(1)">
          <mat-icon>arrow_back</mat-icon>
          Back: Create Groups
        </button>
        <button mat-raised-button color="primary" (click)="goToStep(3)" 
                [disabled]="groupCategoryMappings.length === 0">
          Next: Map Workareas
          <mat-icon>arrow_forward</mat-icon>
        </button>
      </div>
    </div>

    <!-- Step 3: Group-Workarea Mapping -->
    <div *ngIf="currentStep === 3" class="step-content">
      <div class="step-header">
        <mat-icon>work</mat-icon>
        <h3>Map Groups to Workareas</h3>
        <p>Assign work areas to each department group</p>
      </div>

      <app-group-workarea-mapping
        [departmentGroups]="departmentGroups"
        [availableWorkareas]="allWorkAreas"
        [existingMappings]="groupWorkareaMappings"
        (mappingChanged)="onGroupWorkareaMappingChanged($event)">
      </app-group-workarea-mapping>

      <!-- Step Actions -->
      <div class="step-actions">
        <button mat-button (click)="goToStep(2)">
          <mat-icon>arrow_back</mat-icon>
          Back: Map Categories
        </button>
        <button mat-raised-button color="primary" (click)="saveAndClose()" [disabled]="isSaving">
          <mat-spinner diameter="20" *ngIf="isSaving"></mat-spinner>
          <mat-icon *ngIf="!isSaving">save</mat-icon>
          {{isSaving ? 'Saving...' : 'Save & Close'}}
        </button>
      </div>
    </div>

    <!-- Summary Section -->
    <div class="summary-section" *ngIf="!showAsDialog && departmentGroups.length > 0">
      <div class="section-header">
        <mat-icon>summarize</mat-icon>
        <h4>Configuration Summary</h4>
      </div>
      
      <div class="summary-cards">
        <div class="summary-card">
          <div class="summary-number">{{departmentGroups.length}}</div>
          <div class="summary-label">Groups Created</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{groupCategoryMappings.length}}</div>
          <div class="summary-label">Category Mappings</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{groupWorkareaMappings.length}}</div>
          <div class="summary-label">Workarea Mappings</div>
        </div>

        <div class="summary-card">
          <div class="summary-number">{{departmentGroups.reduce((sum, g) => sum + g.departmentIds.length, 0)}}</div>
          <div class="summary-label">Total Departments</div>
        </div>
      </div>
    </div>

  </div>

  <!-- Dialog Actions (only show in dialog mode) -->
  <div *ngIf="showAsDialog" class="dialog-actions">
    <button mat-button (click)="closeDialog()">Cancel</button>
    <button mat-raised-button color="primary" (click)="saveAndClose()" [disabled]="isSaving">
      <mat-spinner diameter="20" *ngIf="isSaving"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{isSaving ? 'Saving...' : 'Save & Close'}}
    </button>
  </div>
</div>
