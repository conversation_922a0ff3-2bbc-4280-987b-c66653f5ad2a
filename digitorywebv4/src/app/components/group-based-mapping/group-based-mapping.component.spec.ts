import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { GroupBasedMappingComponent } from './group-based-mapping.component';
import { DepartmentService } from '../../services/department.service';
import { DepartmentGroupService } from '../../services/department-group.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

describe('GroupBasedMappingComponent', () => {
  let component: GroupBasedMappingComponent;
  let fixture: ComponentFixture<GroupBasedMappingComponent>;
  let mockDepartmentService: jasmine.SpyObj<DepartmentService>;
  let mockDepartmentGroupService: jasmine.SpyObj<DepartmentGroupService>;
  let mockSmartDashboardService: jasmine.SpyObj<SmartDashboardService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  const mockDepartments = [
    { id: 'dept1', name: 'Food Department', code: 'FOOD' },
    { id: 'dept2', name: 'Beverage Department', code: 'BEV' },
    { id: 'dept3', name: 'Bakery Department', code: 'BAKERY' }
  ];

  const mockCategories = ['Appetizers', 'Main Course', 'Desserts', 'Beverages'];
  const mockWorkAreas = [
    { restaurantIdOld: 'rest1', branchName: 'Branch 1', workAreas: ['Kitchen', 'Bar', 'Bakery'], disabled: false }
  ];

  beforeEach(async () => {
    const departmentServiceSpy = jasmine.createSpyObj('DepartmentService', ['getDepartments']);
    const departmentGroupServiceSpy = jasmine.createSpyObj('DepartmentGroupService', [
      'createDepartmentGroup',
      'deleteDepartmentGroup',
      'loadGroupMappingConfig',
      'saveGroupMappingConfig',
      'departmentGroups$',
      'groupCategoryMappings$',
      'groupWorkareaMappings$'
    ]);
    const smartDashboardServiceSpy = jasmine.createSpyObj('SmartDashboardService', ['getCategories', 'getWorkAreas']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    await TestBed.configureTestingModule({
      imports: [
        GroupBasedMappingComponent,
        ReactiveFormsModule,
        MatSnackBarModule,
        MatFormFieldModule,
        MatSelectModule,
        MatInputModule,
        MatIconModule,
        MatButtonModule,
        MatCardModule,
        MatChipsModule,
        MatProgressSpinnerModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: DepartmentService, useValue: departmentServiceSpy },
        { provide: DepartmentGroupService, useValue: departmentGroupServiceSpy },
        { provide: SmartDashboardService, useValue: smartDashboardServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(GroupBasedMappingComponent);
    component = fixture.componentInstance;
    
    mockDepartmentService = TestBed.inject(DepartmentService) as jasmine.SpyObj<DepartmentService>;
    mockDepartmentGroupService = TestBed.inject(DepartmentGroupService) as jasmine.SpyObj<DepartmentGroupService>;
    mockSmartDashboardService = TestBed.inject(SmartDashboardService) as jasmine.SpyObj<SmartDashboardService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup mock returns
    mockDepartmentService.getDepartments.and.returnValue(of(mockDepartments));
    mockSmartDashboardService.getCategories.and.returnValue(of(mockCategories));
    mockSmartDashboardService.getWorkAreas.and.returnValue(of(mockWorkAreas));
    mockDepartmentGroupService.loadGroupMappingConfig.and.returnValue(of({
      departmentGroups: [],
      groupCategoryMappings: [],
      groupWorkareaMappings: [],
      mappingVersion: '2.0'
    }));
    mockDepartmentGroupService.departmentGroups$ = of([]);
    mockDepartmentGroupService.groupCategoryMappings$ = of([]);
    mockDepartmentGroupService.groupWorkareaMappings$ = of([]);

    component.tenantId = 'test-tenant';
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with step 1', () => {
    expect(component.currentStep).toBe(1);
  });

  it('should load departments on init', async () => {
    await component.ngOnInit();
    fixture.detectChanges();
    
    expect(mockDepartmentService.getDepartments).toHaveBeenCalledWith('test-tenant');
    expect(component.departments).toEqual(mockDepartments);
  });

  it('should load categories on init', async () => {
    await component.ngOnInit();
    fixture.detectChanges();
    
    expect(mockSmartDashboardService.getCategories).toHaveBeenCalledWith('test-tenant');
    expect(component.categories).toEqual(mockCategories);
  });

  it('should load work areas on init', async () => {
    await component.ngOnInit();
    fixture.detectChanges();
    
    expect(mockSmartDashboardService.getWorkAreas).toHaveBeenCalledWith('test-tenant');
    expect(component.workAreas).toEqual(mockWorkAreas);
  });

  it('should create department group', () => {
    const mockGroup = {
      groupId: 'group1',
      groupName: 'Food Group',
      departmentIds: ['dept1', 'dept3'],
      departmentNames: ['Food Department', 'Bakery Department']
    };

    mockDepartmentGroupService.createDepartmentGroup.and.returnValue(of(mockGroup));

    component.newGroupNameCtrl.setValue('Food Group');
    component.selectedDepartmentsForGroupCtrl.setValue(['dept1', 'dept3']);
    component.departments = mockDepartments;

    component.createDepartmentGroup();

    expect(mockDepartmentGroupService.createDepartmentGroup).toHaveBeenCalledWith(
      'test-tenant',
      'Food Group',
      ['dept1', 'dept3'],
      ['Food Department', 'Bakery Department']
    );
  });

  it('should validate group name before creation', () => {
    component.newGroupNameCtrl.setValue('');
    component.selectedDepartmentsForGroupCtrl.setValue(['dept1']);

    spyOn(component as any, 'showError');
    component.createDepartmentGroup();

    expect((component as any).showError).toHaveBeenCalledWith('Please enter a group name');
  });

  it('should validate department selection before creation', () => {
    component.newGroupNameCtrl.setValue('Test Group');
    component.selectedDepartmentsForGroupCtrl.setValue([]);

    spyOn(component as any, 'showError');
    component.createDepartmentGroup();

    expect((component as any).showError).toHaveBeenCalledWith('Please select at least one department');
  });

  it('should navigate between steps correctly', () => {
    component.goToStep(2);
    expect(component.currentStep).toBe(2);

    component.goToStep(3);
    expect(component.currentStep).toBe(3);

    component.goToStep(1);
    expect(component.currentStep).toBe(1);
  });

  it('should check step navigation prerequisites', () => {
    // Step 2 requires department groups
    expect(component.canProceedToStep(2)).toBeFalse();
    
    component.departmentGroups = [
      { groupId: 'g1', groupName: 'Group 1', departmentIds: ['d1'], departmentNames: ['Dept 1'] }
    ];
    expect(component.canProceedToStep(2)).toBeTrue();

    // Step 3 requires both groups and category mappings
    expect(component.canProceedToStep(3)).toBeFalse();
    
    component.groupCategoryMappings = [
      { groupId: 'g1', groupName: 'Group 1', categories: ['Cat 1'] }
    ];
    expect(component.canProceedToStep(3)).toBeTrue();
  });

  it('should save group mappings', () => {
    mockDepartmentGroupService.saveGroupMappingConfig.and.returnValue(of(true));

    component.saveAndClose();

    expect(mockDepartmentGroupService.saveGroupMappingConfig).toHaveBeenCalledWith('test-tenant');
  });

  it('should emit mappings changed event on save', () => {
    mockDepartmentGroupService.saveGroupMappingConfig.and.returnValue(of(true));
    spyOn(component.mappingsChanged, 'emit');

    component.departmentGroups = [{ groupId: 'g1', groupName: 'Group 1', departmentIds: ['d1'], departmentNames: ['Dept 1'] }];
    component.groupCategoryMappings = [{ groupId: 'g1', groupName: 'Group 1', categories: ['Cat 1'] }];
    component.groupWorkareaMappings = [{ groupId: 'g1', groupName: 'Group 1', workAreas: ['WA1'] }];

    component.saveAndClose();

    expect(component.mappingsChanged.emit).toHaveBeenCalledWith({
      departmentGroups: component.departmentGroups,
      groupCategoryMappings: component.groupCategoryMappings,
      groupWorkareaMappings: component.groupWorkareaMappings
    });
  });

  it('should close dialog when requested', () => {
    component.showAsDialog = true;
    spyOn(component.dialogClosed, 'emit');

    component.closeDialog();

    expect(component.dialogClosed.emit).toHaveBeenCalled();
  });

  it('should get department name by id', () => {
    component.departments = mockDepartments;
    
    expect(component.getDepartmentName('dept1')).toBe('Food Department');
    expect(component.getDepartmentName('nonexistent')).toBe('nonexistent');
  });

  it('should filter departments based on search', () => {
    component.departments = mockDepartments;
    component.availableDepartments = mockDepartments;

    (component as any).filterDepartments('food');
    expect(component.filteredDepartments.length).toBe(1);
    expect(component.filteredDepartments[0].name).toBe('Food Department');

    (component as any).filterDepartments('');
    expect(component.filteredDepartments.length).toBe(3);
  });
});
