import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, FormGroup, FormArray, FormBuilder } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatCardModule } from '@angular/material/card';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { Subject, takeUntil, combineLatest } from 'rxjs';

import { DepartmentService, Department } from '../../services/department.service';
import { DepartmentGroupService, DepartmentGroup, GroupCategoryMapping, GroupWorkareaMapping } from '../../services/department-group.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';
import { GroupCategoryMappingComponent } from '../group-category-mapping/group-category-mapping.component';
import { GroupWorkareaMappingComponent } from '../group-workarea-mapping/group-workarea-mapping.component';

export interface WorkAreaData {
  restaurantIdOld: string;
  branchName: string;
  workAreas: string[];
  disabled: boolean;
}

@Component({
  selector: 'app-group-based-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatSnackBarModule,
    MatButtonModule,
    MatInputModule,
    MatChipsModule,
    MatCardModule,
    NgxMatSelectSearchModule,
    GroupCategoryMappingComponent,
    GroupWorkareaMappingComponent
  ],
  templateUrl: './group-based-mapping.component.html',
  styleUrls: ['./group-based-mapping.component.scss']
})
export class GroupBasedMappingComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  // Inputs
  @Input() tenantId: string = '';
  @Input() autoEmit: boolean = true;
  @Input() showAsDialog: boolean = false;

  // Outputs
  @Output() mappingsChanged = new EventEmitter<any>();
  @Output() dialogClosed = new EventEmitter<void>();

  // Data
  departments: Department[] = [];
  categories: string[] = [];
  workAreas: WorkAreaData[] = [];
  allWorkAreas: string[] = [];
  
  // Group management
  departmentGroups: DepartmentGroup[] = [];
  groupCategoryMappings: GroupCategoryMapping[] = [];
  groupWorkareaMappings: GroupWorkareaMapping[] = [];
  
  // Form controls
  newGroupNameCtrl = new FormControl('');
  selectedDepartmentsForGroupCtrl = new FormControl<string[]>([]);
  departmentFilterCtrl = new FormControl('');
  filteredDepartments: Department[] = [];
  
  // State
  isLoading = true;
  isSaving = false;
  currentStep = 1; // 1: Create Groups, 2: Map Categories, 3: Map Workareas
  
  // Step 1: Group Creation
  availableDepartments: Department[] = [];
  
  // Step 2: Category Mapping
  selectedGroupForCategory: string = '';
  availableCategories: string[] = [];
  
  // Step 3: Workarea Mapping
  selectedGroupForWorkarea: string = '';
  availableWorkareas: string[] = [];

  constructor(
    private departmentService: DepartmentService,
    private departmentGroupService: DepartmentGroupService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef,
    private fb: FormBuilder
  ) {
    this.filteredDepartments = this.departments;
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.setupFormSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Load all required data
    Promise.all([
      this.loadDepartments(),
      this.loadCategories(),
      this.loadWorkAreas(),
      this.loadExistingGroupMappings()
    ]).then(() => {
      this.updateAvailableData();
      this.isLoading = false;
      this.cdr.detectChanges();
    }).catch(error => {
      console.error('Error initializing component:', error);
      this.isLoading = false;
      this.showError('Failed to load data');
      this.cdr.detectChanges();
    });
  }

  private setupFormSubscriptions(): void {
    // Filter departments based on search
    this.departmentFilterCtrl.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(searchTerm => {
        this.filterDepartments(searchTerm || '');
      });

    // Subscribe to group service observables
    this.departmentGroupService.departmentGroups$
      .pipe(takeUntil(this.destroy$))
      .subscribe(groups => {
        this.departmentGroups = groups;
        this.updateAvailableData();
        this.cdr.detectChanges();
      });

    this.departmentGroupService.groupCategoryMappings$
      .pipe(takeUntil(this.destroy$))
      .subscribe(mappings => {
        this.groupCategoryMappings = mappings;
        this.updateAvailableCategories();
        this.cdr.detectChanges();
      });

    this.departmentGroupService.groupWorkareaMappings$
      .pipe(takeUntil(this.destroy$))
      .subscribe(mappings => {
        this.groupWorkareaMappings = mappings;
        this.cdr.detectChanges();
      });
  }

  // ===== DATA LOADING METHODS =====

  private loadDepartments(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentService.getDepartments(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (departments) => {
            this.departments = departments;
            this.filteredDepartments = departments;
            resolve();
          },
          error: (error) => {
            console.error('Error loading departments:', error);
            reject(error);
          }
        });
    });
  }

  private loadCategories(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.smartDashboardService.getCategories(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (categories) => {
            this.categories = categories;
            resolve();
          },
          error: (error) => {
            console.error('Error loading categories:', error);
            reject(error);
          }
        });
    });
  }

  private loadWorkAreas(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Get work areas from user's restaurant access data
        const user = this.authService.getCurrentUser();
        const workAreaData: WorkAreaData[] = [];

        if (user && user.restaurantAccess) {
          user.restaurantAccess.forEach((restaurant: any) => {
            if (restaurant.workAreas && restaurant.workAreas.length > 0) {
              workAreaData.push({
                restaurantIdOld: restaurant.restaurantIdOld,
                branchName: restaurant.branchName,
                workAreas: restaurant.workAreas,
                disabled: false
              });
            }
          });
        }

        this.workAreas = workAreaData;
        this.extractAllWorkAreas();
        resolve();
      } catch (error) {
        console.error('Error loading work areas:', error);
        this.workAreas = [];
        this.allWorkAreas = [];
        resolve(); // Don't reject, continue with empty data
      }
    });
  }

  private loadExistingGroupMappings(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.departmentGroupService.loadGroupMappingConfig(this.tenantId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (config) => {
            // Data is automatically updated in the service observables
            resolve();
          },
          error: (error) => {
            console.error('Error loading existing group mappings:', error);
            // Don't reject - continue with empty data
            resolve();
          }
        });
    });
  }

  private extractAllWorkAreas(): void {
    const allAreas = new Set<string>();
    this.workAreas.forEach(wa => {
      wa.workAreas.forEach(area => allAreas.add(area));
    });
    this.allWorkAreas = Array.from(allAreas).sort();
    this.availableWorkareas = [...this.allWorkAreas];
  }

  // ===== UTILITY METHODS =====

  private updateAvailableData(): void {
    this.updateAvailableDepartments();
    this.updateAvailableCategories();
  }

  private updateAvailableDepartments(): void {
    const assignedDepartmentIds = this.departmentGroups.flatMap(g => g.departmentIds);
    this.availableDepartments = this.departments.filter(dept => 
      !assignedDepartmentIds.includes(dept.id)
    );
    this.filterDepartments(this.departmentFilterCtrl.value || '');
  }

  private updateAvailableCategories(): void {
    const assignedCategories = this.groupCategoryMappings.flatMap(m => m.categories);
    this.availableCategories = this.categories.filter(cat => 
      !assignedCategories.includes(cat)
    );
  }

  private filterDepartments(searchTerm: string): void {
    if (!searchTerm) {
      this.filteredDepartments = this.availableDepartments;
    } else {
      const term = searchTerm.toLowerCase();
      this.filteredDepartments = this.availableDepartments.filter(dept =>
        dept.name.toLowerCase().includes(term) ||
        (dept.code && dept.code.toLowerCase().includes(term))
      );
    }
  }

  // ===== STEP NAVIGATION =====

  goToStep(step: number): void {
    if (step >= 1 && step <= 3) {
      this.currentStep = step;
      this.cdr.detectChanges();
    }
  }

  canProceedToStep(step: number): boolean {
    switch (step) {
      case 2:
        return this.departmentGroups.length > 0;
      case 3:
        return this.departmentGroups.length > 0 && this.groupCategoryMappings.length > 0;
      default:
        return true;
    }
  }

  // ===== STEP 1: GROUP CREATION =====

  createDepartmentGroup(): void {
    const groupName = this.newGroupNameCtrl.value?.trim();
    const selectedDepartmentIds = this.selectedDepartmentsForGroupCtrl.value || [];

    if (!groupName) {
      this.showError('Please enter a group name');
      return;
    }

    if (selectedDepartmentIds.length === 0) {
      this.showError('Please select at least one department');
      return;
    }

    // Check for duplicate group name
    if (this.departmentGroups.some(g => g.groupName.toLowerCase() === groupName.toLowerCase())) {
      this.showError('A group with this name already exists');
      return;
    }

    const selectedDepartmentNames = selectedDepartmentIds.map(id => {
      const dept = this.departments.find(d => d.id === id);
      return dept ? dept.name : id;
    });

    this.departmentGroupService.createDepartmentGroup(
      this.tenantId,
      groupName,
      selectedDepartmentIds,
      selectedDepartmentNames
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (group) => {
        this.showSuccess(`Group "${group.groupName}" created successfully`);
        this.resetGroupCreationForm();
      },
      error: (error) => {
        console.error('Error creating group:', error);
        this.showError('Failed to create group');
      }
    });
  }

  deleteDepartmentGroup(groupId: string): void {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    if (!group) return;

    if (confirm(`Are you sure you want to delete the group "${group.groupName}"? This will also remove all associated mappings.`)) {
      this.departmentGroupService.deleteDepartmentGroup(groupId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.showSuccess(`Group "${group.groupName}" deleted successfully`);
          },
          error: (error) => {
            console.error('Error deleting group:', error);
            this.showError('Failed to delete group');
          }
        });
    }
  }

  private resetGroupCreationForm(): void {
    this.newGroupNameCtrl.reset();
    this.selectedDepartmentsForGroupCtrl.reset();
    this.departmentFilterCtrl.reset();
  }

  // ===== UTILITY METHODS FOR DISPLAY =====

  getDepartmentName(departmentId: string): string {
    const dept = this.departments.find(d => d.id === departmentId);
    return dept ? dept.name : departmentId;
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  // ===== STEP 2 & 3 EVENT HANDLERS =====

  onGroupCategoryMappingChanged(mapping: GroupCategoryMapping): void {
    // The service automatically updates the observable, so we just need to detect changes
    this.cdr.detectChanges();
  }

  onGroupWorkareaMappingChanged(mapping: GroupWorkareaMapping): void {
    // The service automatically updates the observable, so we just need to detect changes
    this.cdr.detectChanges();
  }

  // ===== SAVE AND CLOSE =====

  saveAndClose(): void {
    this.isSaving = true;

    this.departmentGroupService.saveGroupMappingConfig(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.showSuccess('Group mappings saved successfully');
            this.mappingsChanged.emit({
              departmentGroups: this.departmentGroups,
              groupCategoryMappings: this.groupCategoryMappings,
              groupWorkareaMappings: this.groupWorkareaMappings
            });
            if (this.showAsDialog) {
              this.dialogClosed.emit();
            }
          } else {
            this.showError('Failed to save group mappings');
          }
          this.isSaving = false;
        },
        error: (error) => {
          console.error('Error saving group mappings:', error);
          this.showError('Failed to save group mappings');
          this.isSaving = false;
        }
      });
  }

  closeDialog(): void {
    if (this.showAsDialog) {
      this.dialogClosed.emit();
    }
  }
}
