.group-based-mapping {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  &.dialog-mode {
    padding: 16px;
    max-width: none;
    margin: 0;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;

    p {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }
  }

  .mapping-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  // Step Navigation
  .step-navigation {
    .step-tabs {
      display: flex;
      gap: 8px;
      border-bottom: 2px solid #e0e0e0;
      padding-bottom: 0;

      .step-tab {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: none;
        background: transparent;
        color: #666;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border-radius: 8px 8px 0 0;
        transition: all 0.3s ease;
        min-width: 160px;
        justify-content: center;

        &:hover:not(:disabled) {
          background-color: #f5f5f5;
          color: #333;
        }

        &.active {
          background-color: #ff6b35;
          color: white;
          border-bottom: 3px solid #ff6b35;
        }

        &.completed:not(.active) {
          background-color: #e8f5e8;
          color: #2e7d32;
          
          mat-icon {
            color: #2e7d32;
          }
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  // Step Content
  .step-content {
    .step-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 24px;
      padding: 16px;
      background: linear-gradient(135deg, #ff6b35 0%, #ff8a65 100%);
      color: white;
      border-radius: 8px;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      h3 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        opacity: 0.9;
        font-size: 14px;
      }
    }
  }

  // Creation Card
  .creation-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    mat-card-header {
      background-color: #f8f9fa;
      margin: -16px -16px 16px -16px;
      padding: 16px;
      border-radius: 8px 8px 0 0;

      mat-card-title {
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .form-row {
      margin-bottom: 16px;

      .group-name-field,
      .department-select-field {
        width: 100%;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }

  // Existing Groups
  .existing-groups {
    h4 {
      color: #333;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    .groups-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .group-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
      }

      mat-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        mat-card-title {
          color: #333;
          font-size: 16px;
          font-weight: 600;
          flex: 1;
        }

        .delete-btn {
          color: #f44336;
          
          &:hover {
            background-color: rgba(244, 67, 54, 0.1);
          }
        }
      }

      .department-chips {
        margin-bottom: 12px;

        mat-chip-listbox {
          mat-chip {
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 12px;
            margin: 2px;
          }
        }
      }

      .group-stats {
        .stat {
          font-size: 12px;
          color: #666;
          background-color: #f5f5f5;
          padding: 4px 8px;
          border-radius: 12px;
        }
      }
    }
  }

  // Empty State
  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 18px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // Step Actions
  .step-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 24px;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 140px;
      justify-content: center;
    }
  }

  // Summary Section
  .summary-section {
    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 16px;

      mat-icon {
        color: #ff6b35;
      }

      h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;

      .summary-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .summary-number {
          font-size: 24px;
          font-weight: 700;
          color: #ff6b35;
          margin-bottom: 4px;
        }

        .summary-label {
          font-size: 12px;
          color: #666;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  // Dialog Actions
  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0 0;
    border-top: 1px solid #e0e0e0;
    margin-top: 24px;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 120px;
      justify-content: center;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .group-based-mapping {
    padding: 12px;

    .step-navigation .step-tabs {
      flex-direction: column;
      gap: 4px;

      .step-tab {
        min-width: auto;
        width: 100%;
      }
    }

    .existing-groups .groups-grid {
      grid-template-columns: 1fr;
    }

    .summary-section .summary-cards {
      grid-template-columns: repeat(2, 1fr);
    }

    .step-actions {
      flex-direction: column;
      gap: 12px;

      button {
        width: 100%;
      }
    }
  }
}
