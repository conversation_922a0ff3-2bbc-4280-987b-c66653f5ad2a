import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatExpansionModule } from '@angular/material/expansion';
import { Subject, takeUntil } from 'rxjs';

import { DepartmentGroup, GroupWorkareaMapping, DepartmentGroupService } from '../../services/department-group.service';

@Component({
  selector: 'app-group-workarea-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatChipsModule,
    MatSnackBarModule,
    MatExpansionModule
  ],
  templateUrl: './group-workarea-mapping.component.html',
  styleUrls: ['./group-workarea-mapping.component.scss']
})
export class GroupWorkareaMappingComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  @Input() departmentGroups: DepartmentGroup[] = [];
  @Input() availableWorkareas: string[] = [];
  @Input() existingMappings: GroupWorkareaMapping[] = [];
  @Output() mappingChanged = new EventEmitter<GroupWorkareaMapping>();

  selectedGroupId: string = '';
  selectedWorkAreasCtrl = new FormControl<string[]>([]);
  
  currentGroupMapping: GroupWorkareaMapping | null = null;
  workareaConflicts: Map<string, string[]> = new Map(); // workarea -> groups using it
  
  constructor(
    private departmentGroupService: DepartmentGroupService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.analyzeWorkareaConflicts();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSubscriptions(): void {
    // Subscribe to workarea mapping changes
    this.departmentGroupService.groupWorkareaMappings$
      .pipe(takeUntil(this.destroy$))
      .subscribe(mappings => {
        this.existingMappings = mappings;
        this.analyzeWorkareaConflicts();
        this.updateCurrentGroupMapping();
        this.cdr.detectChanges();
      });
  }

  onGroupSelected(groupId: string): void {
    this.selectedGroupId = groupId;
    this.updateCurrentGroupMapping();
  }

  private updateCurrentGroupMapping(): void {
    if (this.selectedGroupId) {
      this.currentGroupMapping = this.existingMappings.find(m => m.groupId === this.selectedGroupId) || null;
      
      if (this.currentGroupMapping) {
        this.selectedWorkAreasCtrl.setValue(this.currentGroupMapping.workAreas);
      } else {
        this.selectedWorkAreasCtrl.setValue([]);
      }
    } else {
      this.currentGroupMapping = null;
      this.selectedWorkAreasCtrl.setValue([]);
    }
  }

  private analyzeWorkareaConflicts(): void {
    this.workareaConflicts.clear();
    
    // Track which groups use each workarea
    this.existingMappings.forEach(mapping => {
      mapping.workAreas.forEach(workarea => {
        if (!this.workareaConflicts.has(workarea)) {
          this.workareaConflicts.set(workarea, []);
        }
        this.workareaConflicts.get(workarea)!.push(mapping.groupName);
      });
    });
  }

  saveGroupWorkareaMapping(): void {
    if (!this.selectedGroupId) {
      this.showError('Please select a group first');
      return;
    }

    const selectedWorkareas = this.selectedWorkAreasCtrl.value || [];
    if (selectedWorkareas.length === 0) {
      this.showError('Please select at least one workarea');
      return;
    }

    const selectedGroup = this.departmentGroups.find(g => g.groupId === this.selectedGroupId);
    if (!selectedGroup) {
      this.showError('Selected group not found');
      return;
    }

    // Check for conflicts and create virtual workareas if needed
    const processedMapping = this.processWorkareaConflicts(selectedWorkareas, selectedGroup);

    this.departmentGroupService.setGroupWorkareaMapping(
      this.selectedGroupId,
      selectedGroup.groupName,
      processedMapping.workAreas,
      processedMapping.virtualWorkAreas
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (mapping) => {
        this.showSuccess(`Workareas mapped to "${selectedGroup.groupName}" successfully`);
        if (processedMapping.virtualWorkAreas && processedMapping.virtualWorkAreas.length > 0) {
          this.showInfo(`${processedMapping.virtualWorkAreas.length} virtual workareas created to resolve conflicts`);
        }
        this.mappingChanged.emit(mapping);
        this.resetForm();
      },
      error: (error) => {
        console.error('Error saving group-workarea mapping:', error);
        this.showError('Failed to save workarea mapping');
      }
    });
  }

  private processWorkareaConflicts(selectedWorkareas: string[], selectedGroup: DepartmentGroup): {
    workAreas: string[];
    virtualWorkAreas: any[];
  } {
    const finalWorkAreas: string[] = [];
    const virtualWorkAreas: any[] = [];

    selectedWorkareas.forEach(workarea => {
      const conflictingGroups = this.workareaConflicts.get(workarea) || [];
      const hasConflict = conflictingGroups.length > 0 && !conflictingGroups.includes(selectedGroup.groupName);

      if (hasConflict) {
        // Create virtual workarea
        const virtualWorkareaName = `${workarea}_${selectedGroup.groupName.replace(/\s+/g, '_')}`;
        virtualWorkAreas.push({
          physicalWorkarea: workarea,
          virtualWorkarea: virtualWorkareaName,
          groupId: selectedGroup.groupId,
          groupName: selectedGroup.groupName,
          isVirtual: true
        });
        finalWorkAreas.push(virtualWorkareaName);
      } else {
        // Use physical workarea directly
        finalWorkAreas.push(workarea);
      }
    });

    return {
      workAreas: finalWorkAreas,
      virtualWorkAreas
    };
  }

  removeGroupWorkareaMapping(groupId: string): void {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    if (!group) return;

    if (confirm(`Are you sure you want to remove workarea mappings for "${group.groupName}"?`)) {
      this.departmentGroupService.setGroupWorkareaMapping(groupId, group.groupName, [])
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.showSuccess(`Workarea mappings removed for "${group.groupName}"`);
            if (this.selectedGroupId === groupId) {
              this.resetForm();
            }
          },
          error: (error) => {
            console.error('Error removing group-workarea mapping:', error);
            this.showError('Failed to remove workarea mapping');
          }
        });
    }
  }

  private resetForm(): void {
    this.selectedGroupId = '';
    this.selectedWorkAreasCtrl.setValue([]);
    this.currentGroupMapping = null;
  }

  getGroupName(groupId: string): string {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    return group ? group.groupName : groupId;
  }

  getGroupDepartmentCount(groupId: string): number {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    return group ? group.departmentIds.length : 0;
  }

  getMappedGroupsCount(): number {
    return this.existingMappings.length;
  }

  getUnmappedGroups(): DepartmentGroup[] {
    const mappedGroupIds = this.existingMappings.map(m => m.groupId);
    return this.departmentGroups.filter(g => !mappedGroupIds.includes(g.groupId));
  }

  isWorkareaConflicted(workarea: string): boolean {
    const conflictingGroups = this.workareaConflicts.get(workarea) || [];
    return conflictingGroups.length > 1;
  }

  getWorkareaConflictInfo(workarea: string): string {
    const conflictingGroups = this.workareaConflicts.get(workarea) || [];
    if (conflictingGroups.length <= 1) return '';
    return `Used by: ${conflictingGroups.join(', ')}`;
  }

  getVirtualWorkareaCount(): number {
    return this.existingMappings.reduce((count, mapping) => {
      return count + (mapping.virtualWorkAreas?.length || 0);
    }, 0);
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  private showInfo(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 4000,
      panelClass: ['info-snackbar']
    });
  }
}
