.group-workarea-mapping {
  display: flex;
  flex-direction: column;
  gap: 24px;

  // Smart Mapping Info
  .smart-mapping-info {
    .info-card {
      background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
      border: 1px solid #bbdefb;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);

      .info-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .info-icon {
          color: #1976d2;
          font-size: 24px;
          width: 24px;
          height: 24px;
          margin-top: 2px;
        }

        .info-content {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            color: #1565c0;
            font-size: 16px;
            font-weight: 600;
          }

          p {
            margin: 0;
            color: #424242;
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }

      .info-stats {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #e1f5fe;

        .stat-badge {
          background-color: #1976d2;
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }

  // Mapping Form Card
  .mapping-form-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    mat-card-header {
      background-color: #f8f9fa;
      margin: -16px -16px 16px -16px;
      padding: 16px;
      border-radius: 8px 8px 0 0;

      mat-card-title {
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }

      mat-card-subtitle {
        color: #666;
        font-size: 14px;
        margin-top: 4px;
      }
    }

    .form-row {
      margin-bottom: 16px;

      .group-select-field,
      .workarea-select-field {
        width: 100%;
      }

      .workarea-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .workarea-name {
          flex: 1;
        }

        .conflict-icon {
          color: #ff9800;
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }

      mat-hint {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;

        .hint-icon,
        .inline-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }

        .inline-icon {
          color: #ff9800;
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 20px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
      }
    }
  }

  // Current Mappings
  .current-mappings {
    h4 {
      color: #333;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 20px;
        background-color: #ff6b35;
        border-radius: 2px;
      }
    }

    .mappings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 16px;
    }

    .mapping-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
      }

      mat-card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .mat-mdc-card-header-text {
          flex: 1;
        }

        mat-card-title {
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        mat-card-subtitle {
          color: #666;
          font-size: 12px;
          margin-top: 2px;
        }

        .remove-btn {
          color: #f44336;
          margin-left: 8px;
          
          &:hover {
            background-color: rgba(244, 67, 54, 0.1);
          }
        }
      }

      .workarea-section {
        margin-bottom: 16px;

        .section-label {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 8px;
          font-size: 13px;
          font-weight: 500;
          color: #666;

          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }

        .workarea-chips {
          mat-chip-listbox {
            mat-chip {
              background-color: #e8f5e8;
              color: #2e7d32;
              font-size: 12px;
              margin: 2px;
              border: 1px solid #c8e6c9;
              display: flex;
              align-items: center;
              gap: 4px;

              &.virtual-chip {
                background-color: #fff3e0;
                color: #f57c00;
                border-color: #ffcc02;

                .virtual-icon {
                  font-size: 14px;
                  width: 14px;
                  height: 14px;
                }
              }
            }
          }
        }
      }

      .virtual-info {
        margin-top: 12px;

        .virtual-panel {
          box-shadow: none;
          border: 1px solid #e0e0e0;
          border-radius: 4px;

          mat-panel-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #f57c00;

            mat-icon {
              font-size: 16px;
              width: 16px;
              height: 16px;
            }
          }

          .virtual-list {
            .virtual-item {
              padding: 8px 0;
              border-bottom: 1px solid #f5f5f5;

              &:last-child {
                border-bottom: none;
              }

              .virtual-mapping {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;

                .physical {
                  background-color: #e3f2fd;
                  color: #1976d2;
                  padding: 2px 8px;
                  border-radius: 12px;
                  font-weight: 500;
                }

                .arrow {
                  color: #666;
                  font-size: 16px;
                  width: 16px;
                  height: 16px;
                }

                .virtual {
                  background-color: #fff3e0;
                  color: #f57c00;
                  padding: 2px 8px;
                  border-radius: 12px;
                  font-weight: 500;
                }
              }
            }
          }
        }
      }

      mat-card-actions {
        padding: 8px 16px;
        border-top: 1px solid #e0e0e0;

        button {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
        }
      }
    }
  }

  // Unmapped Groups Warning
  .unmapped-groups {
    .warning-card {
      border-radius: 8px;
      border-left: 4px solid #ff9800;
      background-color: #fff8e1;
      box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);

      mat-card-header {
        display: flex;
        align-items: center;
        gap: 12px;

        .warning-icon {
          color: #ff9800;
          font-size: 24px;
          width: 24px;
          height: 24px;
        }

        mat-card-title {
          color: #e65100;
          font-size: 16px;
          font-weight: 600;
        }

        mat-card-subtitle {
          color: #f57c00;
          font-size: 14px;
        }
      }

      .unmapped-list {
        mat-chip-listbox {
          .unmapped-chip {
            background-color: #ffcc02;
            color: #e65100;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 4px;

            &:hover {
              background-color: #ffb300;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  // Empty States
  .empty-state,
  .no-workareas-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 18px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // Progress Indicator
  .progress-indicator {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .progress-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .progress-value {
        font-size: 12px;
        color: #666;
        background-color: #f5f5f5;
        padding: 2px 8px;
        border-radius: 10px;
      }
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #ff6b35 0%, #ff8a65 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .group-workarea-mapping {
    .current-mappings .mappings-grid {
      grid-template-columns: 1fr;
    }

    .mapping-form-card .form-actions {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
      }
    }
  }
}
