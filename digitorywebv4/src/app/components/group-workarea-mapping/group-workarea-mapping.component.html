<div class="group-workarea-mapping">
  
  <!-- Smart Mapping Info -->
  <div class="smart-mapping-info">
    <mat-card class="info-card">
      <mat-card-content>
        <div class="info-header">
          <mat-icon class="info-icon">auto_awesome</mat-icon>
          <div class="info-content">
            <h4>Smart Workarea Mapping</h4>
            <p>Select any workarea for any group. The system will automatically create virtual workareas to resolve conflicts when multiple groups use the same physical workarea.</p>
          </div>
        </div>
        <div class="info-stats" *ngIf="getVirtualWorkareaCount() > 0">
          <span class="stat-badge">{{getVirtualWorkareaCount()}} virtual workareas created</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Mapping Form -->
  <mat-card class="mapping-form-card">
    <mat-card-header>
      <mat-card-title>Assign Workareas to Groups</mat-card-title>
      <mat-card-subtitle>Select a group and assign workareas to it</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="group-select-field">
          <mat-label>Select Group</mat-label>
          <mat-select [(value)]="selectedGroupId" (selectionChange)="onGroupSelected($event.value)">
            <mat-option value="">-- Select a Group --</mat-option>
            <mat-option *ngFor="let group of departmentGroups" [value]="group.groupId">
              {{group.groupName}} ({{group.departmentIds.length}} departments)
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row" *ngIf="selectedGroupId">
        <mat-form-field appearance="outline" class="workarea-select-field">
          <mat-label>Select Workareas</mat-label>
          <mat-select [formControl]="selectedWorkAreasCtrl" multiple>
            <mat-option *ngFor="let workarea of availableWorkareas" [value]="workarea">
              <div class="workarea-option">
                <span class="workarea-name">{{workarea}}</span>
                <mat-icon *ngIf="isWorkareaConflicted(workarea)" 
                         class="conflict-icon" 
                         [title]="getWorkareaConflictInfo(workarea)">
                  warning
                </mat-icon>
              </div>
            </mat-option>
          </mat-select>
          <mat-hint>
            <mat-icon class="hint-icon">info</mat-icon>
            Workareas with <mat-icon class="inline-icon">warning</mat-icon> are used by other groups and will create virtual workareas
          </mat-hint>
        </mat-form-field>
      </div>

      <div class="form-actions" *ngIf="selectedGroupId">
        <button mat-button (click)="resetForm()" type="button">
          <mat-icon>clear</mat-icon>
          Clear
        </button>
        <button mat-raised-button color="primary" (click)="saveGroupWorkareaMapping()"
                [disabled]="!selectedWorkAreasCtrl.value?.length">
          <mat-icon>save</mat-icon>
          Save Mapping
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Current Mappings -->
  <div class="current-mappings" *ngIf="existingMappings.length > 0">
    <h4>Current Workarea Mappings ({{getMappedGroupsCount()}}/{{departmentGroups.length}})</h4>
    
    <div class="mappings-grid">
      <mat-card *ngFor="let mapping of existingMappings" class="mapping-card">
        <mat-card-header>
          <mat-card-title>{{mapping.groupName}}</mat-card-title>
          <mat-card-subtitle>{{getGroupDepartmentCount(mapping.groupId)}} departments</mat-card-subtitle>
          <button mat-icon-button (click)="removeGroupWorkareaMapping(mapping.groupId)" 
                  class="remove-btn" title="Remove mapping">
            <mat-icon>delete</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <!-- Physical Workareas -->
          <div class="workarea-section" *ngIf="mapping.workAreas.length > 0">
            <div class="section-label">
              <mat-icon>work</mat-icon>
              <span>Workareas ({{mapping.workAreas.length}})</span>
            </div>
            <div class="workarea-chips">
              <mat-chip-listbox>
                <mat-chip *ngFor="let workarea of mapping.workAreas"
                         [class.virtual-chip]="workarea.includes('_')">
                  {{workarea}}
                  <mat-icon *ngIf="workarea.includes('_')" class="virtual-icon">auto_awesome</mat-icon>
                </mat-chip>
              </mat-chip-listbox>
            </div>
          </div>

          <!-- Virtual Workareas Info -->
          <div class="virtual-info" *ngIf="mapping.virtualWorkAreas && mapping.virtualWorkAreas.length > 0">
            <mat-expansion-panel class="virtual-panel">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <mat-icon>auto_awesome</mat-icon>
                  Virtual Workareas ({{mapping.virtualWorkAreas.length}})
                </mat-panel-title>
              </mat-expansion-panel-header>
              <div class="virtual-list">
                <div *ngFor="let virtual of mapping.virtualWorkAreas" class="virtual-item">
                  <div class="virtual-mapping">
                    <span class="physical">{{virtual.physicalWorkarea}}</span>
                    <mat-icon class="arrow">arrow_forward</mat-icon>
                    <span class="virtual">{{virtual.virtualWorkarea}}</span>
                  </div>
                </div>
              </div>
            </mat-expansion-panel>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button (click)="onGroupSelected(mapping.groupId)" color="primary">
            <mat-icon>edit</mat-icon>
            Edit
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>

  <!-- Unmapped Groups Warning -->
  <div class="unmapped-groups" *ngIf="getUnmappedGroups().length > 0">
    <mat-card class="warning-card">
      <mat-card-header>
        <mat-icon class="warning-icon">warning</mat-icon>
        <mat-card-title>Unmapped Groups</mat-card-title>
        <mat-card-subtitle>The following groups don't have workarea mappings yet</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="unmapped-list">
          <mat-chip-listbox>
            <mat-chip *ngFor="let group of getUnmappedGroups()" 
                     (click)="onGroupSelected(group.groupId)"
                     class="unmapped-chip">
              {{group.groupName}}
            </mat-chip>
          </mat-chip-listbox>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="departmentGroups.length === 0">
    <mat-icon class="empty-icon">work</mat-icon>
    <h4>No Groups Available</h4>
    <p>Please create department groups first before mapping workareas.</p>
  </div>

  <!-- No Workareas Available -->
  <div class="no-workareas-state" *ngIf="departmentGroups.length > 0 && availableWorkareas.length === 0">
    <mat-icon class="empty-icon">work</mat-icon>
    <h4>No Workareas Available</h4>
    <p>No workareas are available in the system for mapping.</p>
  </div>

  <!-- Progress Indicator -->
  <div class="progress-indicator" *ngIf="departmentGroups.length > 0">
    <div class="progress-header">
      <span class="progress-label">Mapping Progress</span>
      <span class="progress-value">{{getMappedGroupsCount()}}/{{departmentGroups.length}} groups mapped</span>
    </div>
    <div class="progress-bar">
      <div class="progress-fill" 
           [style.width.%]="(getMappedGroupsCount() / departmentGroups.length) * 100">
      </div>
    </div>
  </div>

</div>
