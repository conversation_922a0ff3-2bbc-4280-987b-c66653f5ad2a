import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { Subject, takeUntil } from 'rxjs';

import { DepartmentGroup, GroupCategoryMapping, DepartmentGroupService } from '../../services/department-group.service';

@Component({
  selector: 'app-group-category-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatChipsModule,
    MatSnackBarModule
  ],
  templateUrl: './group-category-mapping.component.html',
  styleUrls: ['./group-category-mapping.component.scss']
})
export class GroupCategoryMappingComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  @Input() departmentGroups: DepartmentGroup[] = [];
  @Input() availableCategories: string[] = [];
  @Input() existingMappings: GroupCategoryMapping[] = [];
  @Output() mappingChanged = new EventEmitter<GroupCategoryMapping>();

  selectedGroupId: string = '';
  selectedCategoriesCtrl = new FormControl<string[]>([]);
  
  currentGroupMapping: GroupCategoryMapping | null = null;
  availableCategoriesForGroup: string[] = [];

  constructor(
    private departmentGroupService: DepartmentGroupService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.updateAvailableCategories();
    this.setupSubscriptions();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSubscriptions(): void {
    // Subscribe to category mapping changes
    this.departmentGroupService.groupCategoryMappings$
      .pipe(takeUntil(this.destroy$))
      .subscribe(mappings => {
        this.existingMappings = mappings;
        this.updateAvailableCategories();
        this.updateCurrentGroupMapping();
        this.cdr.detectChanges();
      });
  }

  onGroupSelected(groupId: string): void {
    this.selectedGroupId = groupId;
    this.updateCurrentGroupMapping();
    this.updateAvailableCategories();
  }

  private updateCurrentGroupMapping(): void {
    if (this.selectedGroupId) {
      this.currentGroupMapping = this.existingMappings.find(m => m.groupId === this.selectedGroupId) || null;
      
      if (this.currentGroupMapping) {
        this.selectedCategoriesCtrl.setValue(this.currentGroupMapping.categories);
      } else {
        this.selectedCategoriesCtrl.setValue([]);
      }
    } else {
      this.currentGroupMapping = null;
      this.selectedCategoriesCtrl.setValue([]);
    }
  }

  private updateAvailableCategories(): void {
    if (!this.selectedGroupId) {
      this.availableCategoriesForGroup = [...this.availableCategories];
      return;
    }

    // Get categories already assigned to other groups (excluding current group)
    const assignedCategories = this.existingMappings
      .filter(m => m.groupId !== this.selectedGroupId)
      .flatMap(m => m.categories);

    // Add categories currently assigned to this group
    const currentGroupCategories = this.currentGroupMapping?.categories || [];
    
    this.availableCategoriesForGroup = [
      ...this.availableCategories.filter(cat => !assignedCategories.includes(cat)),
      ...currentGroupCategories
    ].sort();
  }

  saveGroupCategoryMapping(): void {
    if (!this.selectedGroupId) {
      this.showError('Please select a group first');
      return;
    }

    const selectedCategories = this.selectedCategoriesCtrl.value || [];
    if (selectedCategories.length === 0) {
      this.showError('Please select at least one category');
      return;
    }

    const selectedGroup = this.departmentGroups.find(g => g.groupId === this.selectedGroupId);
    if (!selectedGroup) {
      this.showError('Selected group not found');
      return;
    }

    this.departmentGroupService.setGroupCategoryMapping(
      this.selectedGroupId,
      selectedGroup.groupName,
      selectedCategories
    ).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: (mapping) => {
        this.showSuccess(`Categories mapped to "${selectedGroup.groupName}" successfully`);
        this.mappingChanged.emit(mapping);
        this.resetForm();
      },
      error: (error) => {
        console.error('Error saving group-category mapping:', error);
        this.showError('Failed to save category mapping');
      }
    });
  }

  removeGroupCategoryMapping(groupId: string): void {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    if (!group) return;

    if (confirm(`Are you sure you want to remove category mappings for "${group.groupName}"?`)) {
      this.departmentGroupService.setGroupCategoryMapping(groupId, group.groupName, [])
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            this.showSuccess(`Category mappings removed for "${group.groupName}"`);
            if (this.selectedGroupId === groupId) {
              this.resetForm();
            }
          },
          error: (error) => {
            console.error('Error removing group-category mapping:', error);
            this.showError('Failed to remove category mapping');
          }
        });
    }
  }

  private resetForm(): void {
    this.selectedGroupId = '';
    this.selectedCategoriesCtrl.setValue([]);
    this.currentGroupMapping = null;
    this.updateAvailableCategories();
  }

  getGroupName(groupId: string): string {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    return group ? group.groupName : groupId;
  }

  getGroupDepartmentCount(groupId: string): number {
    const group = this.departmentGroups.find(g => g.groupId === groupId);
    return group ? group.departmentIds.length : 0;
  }

  getMappedGroupsCount(): number {
    return this.existingMappings.length;
  }

  getUnmappedGroups(): DepartmentGroup[] {
    const mappedGroupIds = this.existingMappings.map(m => m.groupId);
    return this.departmentGroups.filter(g => !mappedGroupIds.includes(g.groupId));
  }

  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
}
