<div class="group-category-mapping">
  
  <!-- Mapping Form -->
  <mat-card class="mapping-form-card">
    <mat-card-header>
      <mat-card-title>Assign Categories to Groups</mat-card-title>
      <mat-card-subtitle>Select a group and assign categories to it</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="group-select-field">
          <mat-label>Select Group</mat-label>
          <mat-select [(value)]="selectedGroupId" (selectionChange)="onGroupSelected($event.value)">
            <mat-option value="">-- Select a Group --</mat-option>
            <mat-option *ngFor="let group of departmentGroups" [value]="group.groupId">
              {{group.groupName}} ({{group.departmentIds.length}} departments)
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="form-row" *ngIf="selectedGroupId">
        <mat-form-field appearance="outline" class="category-select-field">
          <mat-label>Select Categories</mat-label>
          <mat-select [formControl]="selectedCategoriesCtrl" multiple>
            <mat-option *ngFor="let category of availableCategoriesForGroup" [value]="category">
              {{category}}
            </mat-option>
          </mat-select>
          <mat-hint *ngIf="availableCategoriesForGroup.length === 0">
            No categories available. All categories have been assigned to other groups.
          </mat-hint>
        </mat-form-field>
      </div>

      <div class="form-actions" *ngIf="selectedGroupId">
        <button mat-button (click)="resetForm()" type="button">
          <mat-icon>clear</mat-icon>
          Clear
        </button>
        <button mat-raised-button color="primary" (click)="saveGroupCategoryMapping()"
                [disabled]="!selectedCategoriesCtrl.value?.length">
          <mat-icon>save</mat-icon>
          Save Mapping
        </button>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Current Mappings -->
  <div class="current-mappings" *ngIf="existingMappings.length > 0">
    <h4>Current Category Mappings ({{getMappedGroupsCount()}}/{{departmentGroups.length}})</h4>
    
    <div class="mappings-grid">
      <mat-card *ngFor="let mapping of existingMappings" class="mapping-card">
        <mat-card-header>
          <mat-card-title>{{mapping.groupName}}</mat-card-title>
          <mat-card-subtitle>{{getGroupDepartmentCount(mapping.groupId)}} departments</mat-card-subtitle>
          <button mat-icon-button (click)="removeGroupCategoryMapping(mapping.groupId)" 
                  class="remove-btn" title="Remove mapping">
            <mat-icon>delete</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="category-chips">
            <mat-chip-listbox>
              <mat-chip *ngFor="let category of mapping.categories">
                {{category}}
              </mat-chip>
            </mat-chip-listbox>
          </div>
          <div class="mapping-stats">
            <span class="stat">{{mapping.categories.length}} categories</span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-button (click)="onGroupSelected(mapping.groupId)" color="primary">
            <mat-icon>edit</mat-icon>
            Edit
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>

  <!-- Unmapped Groups Warning -->
  <div class="unmapped-groups" *ngIf="getUnmappedGroups().length > 0">
    <mat-card class="warning-card">
      <mat-card-header>
        <mat-icon class="warning-icon">warning</mat-icon>
        <mat-card-title>Unmapped Groups</mat-card-title>
        <mat-card-subtitle>The following groups don't have category mappings yet</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="unmapped-list">
          <mat-chip-listbox>
            <mat-chip *ngFor="let group of getUnmappedGroups()" 
                     (click)="onGroupSelected(group.groupId)"
                     class="unmapped-chip">
              {{group.groupName}}
            </mat-chip>
          </mat-chip-listbox>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="departmentGroups.length === 0">
    <mat-icon class="empty-icon">category</mat-icon>
    <h4>No Groups Available</h4>
    <p>Please create department groups first before mapping categories.</p>
  </div>

  <!-- No Categories Available -->
  <div class="no-categories-state" *ngIf="departmentGroups.length > 0 && availableCategories.length === 0">
    <mat-icon class="empty-icon">category</mat-icon>
    <h4>No Categories Available</h4>
    <p>All categories have been assigned to groups or no categories are available in the system.</p>
  </div>

  <!-- Progress Indicator -->
  <div class="progress-indicator" *ngIf="departmentGroups.length > 0">
    <div class="progress-header">
      <span class="progress-label">Mapping Progress</span>
      <span class="progress-value">{{getMappedGroupsCount()}}/{{departmentGroups.length}} groups mapped</span>
    </div>
    <div class="progress-bar">
      <div class="progress-fill" 
           [style.width.%]="(getMappedGroupsCount() / departmentGroups.length) * 100">
      </div>
    </div>
  </div>

</div>
