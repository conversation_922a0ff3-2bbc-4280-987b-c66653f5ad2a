.group-category-mapping {
  display: flex;
  flex-direction: column;
  gap: 24px;

  // Mapping Form Card
  .mapping-form-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    mat-card-header {
      background-color: #f8f9fa;
      margin: -16px -16px 16px -16px;
      padding: 16px;
      border-radius: 8px 8px 0 0;

      mat-card-title {
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }

      mat-card-subtitle {
        color: #666;
        font-size: 14px;
        margin-top: 4px;
      }
    }

    .form-row {
      margin-bottom: 16px;

      .group-select-field,
      .category-select-field {
        width: 100%;
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 20px;

      button {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
      }
    }
  }

  // Current Mappings
  .current-mappings {
    h4 {
      color: #333;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 20px;
        background-color: #ff6b35;
        border-radius: 2px;
      }
    }

    .mappings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 16px;
    }

    .mapping-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
      }

      mat-card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .mat-mdc-card-header-text {
          flex: 1;
        }

        mat-card-title {
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        mat-card-subtitle {
          color: #666;
          font-size: 12px;
          margin-top: 2px;
        }

        .remove-btn {
          color: #f44336;
          margin-left: 8px;
          
          &:hover {
            background-color: rgba(244, 67, 54, 0.1);
          }
        }
      }

      .category-chips {
        margin-bottom: 12px;

        mat-chip-listbox {
          mat-chip {
            background-color: #e8f5e8;
            color: #2e7d32;
            font-size: 12px;
            margin: 2px;
            border: 1px solid #c8e6c9;
          }
        }
      }

      .mapping-stats {
        .stat {
          font-size: 12px;
          color: #666;
          background-color: #f5f5f5;
          padding: 4px 8px;
          border-radius: 12px;
        }
      }

      mat-card-actions {
        padding: 8px 16px;
        border-top: 1px solid #e0e0e0;

        button {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
        }
      }
    }
  }

  // Unmapped Groups Warning
  .unmapped-groups {
    .warning-card {
      border-radius: 8px;
      border-left: 4px solid #ff9800;
      background-color: #fff8e1;
      box-shadow: 0 2px 8px rgba(255, 152, 0, 0.1);

      mat-card-header {
        display: flex;
        align-items: center;
        gap: 12px;

        .warning-icon {
          color: #ff9800;
          font-size: 24px;
          width: 24px;
          height: 24px;
        }

        mat-card-title {
          color: #e65100;
          font-size: 16px;
          font-weight: 600;
        }

        mat-card-subtitle {
          color: #f57c00;
          font-size: 14px;
        }
      }

      .unmapped-list {
        mat-chip-listbox {
          .unmapped-chip {
            background-color: #ffcc02;
            color: #e65100;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 4px;

            &:hover {
              background-color: #ffb300;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }

  // Empty States
  .empty-state,
  .no-categories-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 18px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // Progress Indicator
  .progress-indicator {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;

    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .progress-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .progress-value {
        font-size: 12px;
        color: #666;
        background-color: #f5f5f5;
        padding: 2px 8px;
        border-radius: 10px;
      }
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: #e0e0e0;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #ff6b35 0%, #ff8a65 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .group-category-mapping {
    .current-mappings .mappings-grid {
      grid-template-columns: 1fr;
    }

    .mapping-form-card .form-actions {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
      }
    }
  }
}
