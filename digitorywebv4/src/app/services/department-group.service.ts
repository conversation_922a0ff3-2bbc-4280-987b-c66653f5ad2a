import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface DepartmentGroup {
  groupId: string;
  groupName: string;
  departmentIds: string[];
  departmentNames: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface GroupCategoryMapping {
  groupId: string;
  groupName: string;
  categories: string[];
}

export interface GroupWorkareaMapping {
  groupId: string;
  groupName: string;
  workAreas: string[];
  virtualWorkAreas?: any[];
}

export interface GroupMappingConfig {
  departmentGroups: DepartmentGroup[];
  groupCategoryMappings: GroupCategoryMapping[];
  groupWorkareaMappings: GroupWorkareaMapping[];
  lastUpdated?: string;
  mappingVersion?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentGroupService {
  private readonly engineUrl = environment.engineUrl;
  
  // State management
  private departmentGroupsSubject = new BehaviorSubject<DepartmentGroup[]>([]);
  public departmentGroups$ = this.departmentGroupsSubject.asObservable();
  
  private groupCategoryMappingsSubject = new BehaviorSubject<GroupCategoryMapping[]>([]);
  public groupCategoryMappings$ = this.groupCategoryMappingsSubject.asObservable();
  
  private groupWorkareaMappingsSubject = new BehaviorSubject<GroupWorkareaMapping[]>([]);
  public groupWorkareaMappings$ = this.groupWorkareaMappingsSubject.asObservable();

  constructor(private readonly http: HttpClient) {}

  // ===== DEPARTMENT GROUP MANAGEMENT =====
  
  /**
   * Create a new department group
   */
  createDepartmentGroup(tenantId: string, groupName: string, departmentIds: string[], departmentNames: string[]): Observable<DepartmentGroup> {
    const groupId = this.generateGroupId();
    const newGroup: DepartmentGroup = {
      groupId,
      groupName,
      departmentIds,
      departmentNames,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Update local state
    const currentGroups = this.departmentGroupsSubject.value;
    const updatedGroups = [...currentGroups, newGroup];
    this.departmentGroupsSubject.next(updatedGroups);

    return new Observable(observer => {
      observer.next(newGroup);
      observer.complete();
    });
  }

  /**
   * Update an existing department group
   */
  updateDepartmentGroup(groupId: string, updates: Partial<DepartmentGroup>): Observable<DepartmentGroup> {
    const currentGroups = this.departmentGroupsSubject.value;
    const groupIndex = currentGroups.findIndex(g => g.groupId === groupId);
    
    if (groupIndex === -1) {
      return throwError(() => new Error('Department group not found'));
    }

    const updatedGroup = {
      ...currentGroups[groupIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    const updatedGroups = [...currentGroups];
    updatedGroups[groupIndex] = updatedGroup;
    this.departmentGroupsSubject.next(updatedGroups);

    return new Observable(observer => {
      observer.next(updatedGroup);
      observer.complete();
    });
  }

  /**
   * Delete a department group
   */
  deleteDepartmentGroup(groupId: string): Observable<boolean> {
    const currentGroups = this.departmentGroupsSubject.value;
    const filteredGroups = currentGroups.filter(g => g.groupId !== groupId);
    this.departmentGroupsSubject.next(filteredGroups);

    // Also remove related mappings
    this.removeGroupMappings(groupId);

    return new Observable(observer => {
      observer.next(true);
      observer.complete();
    });
  }

  /**
   * Get all department groups
   */
  getDepartmentGroups(): Observable<DepartmentGroup[]> {
    return this.departmentGroups$;
  }

  /**
   * Get a specific department group by ID
   */
  getDepartmentGroup(groupId: string): Observable<DepartmentGroup | null> {
    return this.departmentGroups$.pipe(
      map(groups => groups.find(g => g.groupId === groupId) || null)
    );
  }

  /**
   * Check if a department is already assigned to any group
   */
  isDepartmentAssigned(departmentId: string): Observable<boolean> {
    return this.departmentGroups$.pipe(
      map(groups => groups.some(g => g.departmentIds.includes(departmentId)))
    );
  }

  /**
   * Get available departments (not assigned to any group)
   */
  getAvailableDepartments(allDepartments: any[]): Observable<any[]> {
    return this.departmentGroups$.pipe(
      map(groups => {
        const assignedDepartmentIds = groups.flatMap(g => g.departmentIds);
        return allDepartments.filter(dept => !assignedDepartmentIds.includes(dept.id));
      })
    );
  }

  // ===== GROUP-CATEGORY MAPPING =====
  
  /**
   * Create or update group-category mapping
   */
  setGroupCategoryMapping(groupId: string, groupName: string, categories: string[]): Observable<GroupCategoryMapping> {
    const currentMappings = this.groupCategoryMappingsSubject.value;
    const existingIndex = currentMappings.findIndex(m => m.groupId === groupId);
    
    const mapping: GroupCategoryMapping = {
      groupId,
      groupName,
      categories
    };

    let updatedMappings: GroupCategoryMapping[];
    if (existingIndex >= 0) {
      updatedMappings = [...currentMappings];
      updatedMappings[existingIndex] = mapping;
    } else {
      updatedMappings = [...currentMappings, mapping];
    }

    this.groupCategoryMappingsSubject.next(updatedMappings);

    return new Observable(observer => {
      observer.next(mapping);
      observer.complete();
    });
  }

  /**
   * Get group-category mappings
   */
  getGroupCategoryMappings(): Observable<GroupCategoryMapping[]> {
    return this.groupCategoryMappings$;
  }

  /**
   * Get categories for a specific group
   */
  getCategoriesForGroup(groupId: string): Observable<string[]> {
    return this.groupCategoryMappings$.pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.groupId === groupId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Check if a category is already assigned to any group
   */
  isCategoryAssigned(categoryName: string): Observable<boolean> {
    return this.groupCategoryMappings$.pipe(
      map(mappings => mappings.some(m => m.categories.includes(categoryName)))
    );
  }

  // ===== GROUP-WORKAREA MAPPING =====
  
  /**
   * Create or update group-workarea mapping
   */
  setGroupWorkareaMapping(groupId: string, groupName: string, workAreas: string[], virtualWorkAreas?: any[]): Observable<GroupWorkareaMapping> {
    const currentMappings = this.groupWorkareaMappingsSubject.value;
    const existingIndex = currentMappings.findIndex(m => m.groupId === groupId);
    
    const mapping: GroupWorkareaMapping = {
      groupId,
      groupName,
      workAreas,
      virtualWorkAreas: virtualWorkAreas || []
    };

    let updatedMappings: GroupWorkareaMapping[];
    if (existingIndex >= 0) {
      updatedMappings = [...currentMappings];
      updatedMappings[existingIndex] = mapping;
    } else {
      updatedMappings = [...currentMappings, mapping];
    }

    this.groupWorkareaMappingsSubject.next(updatedMappings);

    return new Observable(observer => {
      observer.next(mapping);
      observer.complete();
    });
  }

  /**
   * Get group-workarea mappings
   */
  getGroupWorkareaMappings(): Observable<GroupWorkareaMapping[]> {
    return this.groupWorkareaMappings$;
  }

  /**
   * Get workareas for a specific group
   */
  getWorkareasForGroup(groupId: string): Observable<string[]> {
    return this.groupWorkareaMappings$.pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.groupId === groupId);
        return mapping ? mapping.workAreas : [];
      })
    );
  }

  // ===== PERSISTENCE METHODS =====
  
  /**
   * Load group mapping configuration from backend
   */
  loadGroupMappingConfig(tenantId: string): Observable<GroupMappingConfig> {
    return this.http.get<any>(`${this.engineUrl}master_data/get-mapping-config/${tenantId}`)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            const config: GroupMappingConfig = {
              departmentGroups: response.data.departmentGroups || [],
              groupCategoryMappings: response.data.groupCategoryMappings || [],
              groupWorkareaMappings: response.data.groupWorkareaMappings || [],
              lastUpdated: response.data.lastUpdated,
              mappingVersion: response.data.mappingVersion || '1.0'
            };

            // Update local state
            this.departmentGroupsSubject.next(config.departmentGroups);
            this.groupCategoryMappingsSubject.next(config.groupCategoryMappings);
            this.groupWorkareaMappingsSubject.next(config.groupWorkareaMappings);

            return config;
          }
          return {
            departmentGroups: [],
            groupCategoryMappings: [],
            groupWorkareaMappings: [],
            mappingVersion: '2.0'
          };
        }),
        catchError(error => {
          console.error('DepartmentGroupService: Error loading group mapping config:', error);
          return throwError(() => new Error('Failed to load group mapping configuration'));
        })
      );
  }

  /**
   * Save group mapping configuration to backend
   */
  saveGroupMappingConfig(tenantId: string): Observable<boolean> {
    const requestBody = {
      tenantId,
      departmentGroups: this.departmentGroupsSubject.value,
      groupCategoryMappings: this.groupCategoryMappingsSubject.value,
      groupWorkareaMappings: this.groupWorkareaMappingsSubject.value,
      departmentCategoryMappings: [], // Keep for backward compatibility
      categoryWorkareaMappings: [] // Keep for backward compatibility
    };

    return this.http.post<any>(`${this.engineUrl}master_data/save-mapping-config`, requestBody)
      .pipe(
        map(response => response.success || false),
        catchError(error => {
          console.error('DepartmentGroupService: Error saving group mapping config:', error);
          return throwError(() => new Error('Failed to save group mapping configuration'));
        })
      );
  }

  // ===== UTILITY METHODS =====
  
  /**
   * Generate a unique group ID
   */
  private generateGroupId(): string {
    return 'group_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Remove all mappings for a specific group
   */
  private removeGroupMappings(groupId: string): void {
    // Remove category mappings
    const currentCategoryMappings = this.groupCategoryMappingsSubject.value;
    const filteredCategoryMappings = currentCategoryMappings.filter(m => m.groupId !== groupId);
    this.groupCategoryMappingsSubject.next(filteredCategoryMappings);

    // Remove workarea mappings
    const currentWorkareaMappings = this.groupWorkareaMappingsSubject.value;
    const filteredWorkareaMappings = currentWorkareaMappings.filter(m => m.groupId !== groupId);
    this.groupWorkareaMappingsSubject.next(filteredWorkareaMappings);
  }

  /**
   * Clear all group data (useful for testing or reset)
   */
  clearAllGroupData(): void {
    this.departmentGroupsSubject.next([]);
    this.groupCategoryMappingsSubject.next([]);
    this.groupWorkareaMappingsSubject.next([]);
  }

  /**
   * Validate group configuration
   */
  validateGroupConfiguration(): Observable<{ isValid: boolean; errors: string[] }> {
    return new Observable(observer => {
      const errors: string[] = [];
      const groups = this.departmentGroupsSubject.value;
      const categoryMappings = this.groupCategoryMappingsSubject.value;
      const workareaMappings = this.groupWorkareaMappingsSubject.value;

      // Check for duplicate department assignments
      const allAssignedDepartments = groups.flatMap(g => g.departmentIds);
      const duplicateDepartments = allAssignedDepartments.filter((id, index) => allAssignedDepartments.indexOf(id) !== index);
      if (duplicateDepartments.length > 0) {
        errors.push(`Duplicate department assignments found: ${duplicateDepartments.join(', ')}`);
      }

      // Check for duplicate category assignments
      const allAssignedCategories = categoryMappings.flatMap(m => m.categories);
      const duplicateCategories = allAssignedCategories.filter((cat, index) => allAssignedCategories.indexOf(cat) !== index);
      if (duplicateCategories.length > 0) {
        errors.push(`Duplicate category assignments found: ${duplicateCategories.join(', ')}`);
      }

      observer.next({
        isValid: errors.length === 0,
        errors
      });
      observer.complete();
    });
  }
}
